# Script Integrity Implementation Guide

This guide explains how to implement Subresource Integrity (SRI) for external scripts in the application.

## Overview

Subresource Integrity (SRI) is a security feature that enables browsers to verify that resources they fetch are delivered without unexpected manipulation. It uses cryptographic hashes to ensure script integrity.

## Current Implementation

### 1. Security Headers
The application already has SRI requirements configured in `next.config.js`:
```javascript
{
  key: "Require-SRI-For",
  value: "script style",
}
```

### 2. Script Categories

#### Static Scripts (SRI Recommended)
Scripts that don't change frequently and have predictable URLs:
- Custom libraries
- Specific versions of CDN resources
- Third-party widgets with stable versions

#### Dynamic Scripts (SRI Not Practical)
Scripts that change frequently or have dynamic content:
- Google Tag Manager (`gtm.js`)
- <PERSON><PERSON><PERSON> (`osano.js`)
- Google reCAPTCHA (`api.js`)

## Implementation Examples

### 1. Adding Integrity to Next.js Script Component

```tsx
import Script from "next/script";
import { getScriptIntegrity } from "@/lib/script-integrity";

// Example with static script
<Script
  src="https://example.com/static-library.js"
  integrity="sha256-ABC123DEF456..."
  crossOrigin="anonymous"
  strategy="beforeInteractive"
/>

// Example with dynamic lookup
<Script
  src="https://example.com/library.js"
  integrity={getScriptIntegrity("https://example.com/library.js")}
  crossOrigin="anonymous"
  strategy="beforeInteractive"
/>
```

### 2. Adding Integrity to Dynamically Created Scripts

```typescript
import { createScriptWithIntegrity } from "@/lib/script-integrity";

// Create script with automatic integrity lookup
const script = createScriptWithIntegrity("https://example.com/script.js", {
  async: true,
  defer: true,
  crossOrigin: "anonymous"
});

document.head.appendChild(script);
```

### 3. Managing Known Hashes

Update `lib/script-integrity.ts`:

```typescript
export const SCRIPT_INTEGRITY_HASHES = {
  'https://example.com/static-script.js': 'sha256-ABC123DEF456...',
  'https://cdn.example.com/<EMAIL>': 'sha256-XYZ789GHI012...',
} as const;
```

## Generating SRI Hashes

### Method 1: Command Line (OpenSSL)
```bash
curl -s https://example.com/script.js | openssl dgst -sha256 -binary | openssl base64 -A
```

### Method 2: Online Tools
- [SRI Hash Generator](https://www.srihash.org/)
- [Report URI SRI Hash](https://report-uri.com/home/<USER>

### Method 3: Browser DevTools
1. Open Network tab
2. Load the script
3. Right-click on the script request
4. Select "Copy" → "Copy as cURL"
5. Use the cURL command with OpenSSL

## Current Script Status

### ✅ Implemented with SRI Support
- Custom inline scripts (no external source)
- Utility functions for future static scripts

### ⚠️ Dynamic Scripts (SRI Not Practical)
- **Osano Cookie Consent**: `https://cmp.osano.com/${osanoId}/osano.js`
  - Changes frequently based on configuration
  - Uses `crossOrigin="anonymous"` for security
  
- **Google Tag Manager**: `https://www.googletagmanager.com/gtm.js`
  - Dynamically loaded with environment-specific parameters
  - Protected by CSP and trusted domain policies
  
- **Google reCAPTCHA**: `https://www.google.com/recaptcha/api.js`
  - Updates frequently for security and features
  - Uses `crossOrigin="anonymous"`

## Security Alternatives for Dynamic Scripts

### 1. Content Security Policy (CSP)
Already implemented in `next.config.js` with trusted domains:
```javascript
script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: 
  https://www.googletagmanager.com 
  https://cmp.osano.com 
  https://www.google.com
```

### 2. Nonce-based CSP (Future Enhancement)
For dynamic scripts, consider implementing nonce-based CSP:
```tsx
<Script
  src="https://dynamic-script.com/script.js"
  nonce={generateNonce()}
  crossOrigin="anonymous"
/>
```

### 3. Regular Hash Updates
For critical dynamic scripts, implement automated hash updates:
1. Fetch script content periodically
2. Generate new hash
3. Update configuration
4. Deploy updated hashes

## Best Practices

### ✅ Do
- Always use `crossOrigin="anonymous"` with integrity
- Use SRI for static, versioned scripts
- Monitor for script changes that break SRI
- Have fallback strategies for SRI failures
- Keep hash database updated

### ❌ Don't
- Use SRI for frequently changing scripts without automation
- Ignore SRI failures in production
- Mix HTTP and HTTPS resources
- Use SRI without proper CORS headers

## Troubleshooting

### SRI Failure Symptoms
- Scripts fail to load
- Console errors about integrity mismatch
- Functionality dependent on scripts breaks

### Common Solutions
1. **Hash Mismatch**: Update the integrity hash
2. **CORS Issues**: Ensure `crossOrigin="anonymous"` is set
3. **Script Updated**: Get new hash from provider
4. **Network Issues**: Check if script URL is accessible

### Monitoring
Consider implementing monitoring for:
- SRI failures in production
- Script load failures
- Performance impact of integrity checks

## Future Enhancements

1. **Automated Hash Management**
   - Build-time hash generation
   - CI/CD integration for hash updates
   - Monitoring for script changes

2. **Enhanced CSP**
   - Nonce-based script loading
   - Stricter CSP policies
   - Report-only mode testing

3. **Fallback Strategies**
   - Local script fallbacks
   - Graceful degradation
   - Error reporting and recovery
